import React, { useState, useEffect } from 'react';
import { Box, Typography, styled } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme, useMediaQuery } from '@mui/material';
import { useLanguage } from '../contexts/LanguageContext';
import { useThemeMode } from '../contexts/ThemeContext';

const createTestimonials = (t) => [
  {
    name: t('customers.testimonials.client1.name'),
    role: t('customers.testimonials.client1.position'),
    company: t('customers.testimonials.client1.company'),
    text: t('customers.testimonials.client1.testimonial'),
    rating: '★★★★★',
  },
  {
    name: t('customers.testimonials.client2.name'),
    role: t('customers.testimonials.client2.position'),
    company: t('customers.testimonials.client2.company'),
    text: t('customers.testimonials.client2.testimonial'),
    rating: '★★★★★',
  },
  {
    name: t('customers.testimonials.client3.name'),
    role: t('customers.testimonials.client3.position'),
    company: t('customers.testimonials.client3.company'),
    text: t('customers.testimonials.client3.testimonial'),
    rating: '★★★★★',
  },
];

const Section = styled(Box)(({ theme, isDark }) => ({
  padding: '80px 16px',
  background: isDark ? `
    linear-gradient(135deg,
      rgba(15, 23, 42, 0.97) 0%,
      rgba(30, 41, 59, 0.95) 25%,
      rgba(51, 65, 85, 0.93) 50%,
      rgba(71, 85, 105, 0.95) 75%,
      rgba(100, 116, 139, 0.97) 100%
    ),
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.1) 0%, transparent 50%)
  ` : 'linear-gradient(135deg, #fafafa 0%, #e7ecff 100%)',
  fontFamily: 'Roboto, sans-serif',
  textAlign: 'center',
  position: 'relative',
  overflow: 'hidden',
  '&::before': isDark ? {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      )
    `,
    pointerEvents: 'none',
  } : {},
}));

const GridContainer = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(260px, 1fr))',
  gap: '32px',
  marginTop: '48px',
  padding: '0 12px',
  [theme.breakpoints.down('sm')]: {
    display: 'block',
    overflow: 'hidden',
    width: '100%',
    padding: 0,
  },
}));

const Card = styled(motion.div)(({ theme, isDark }) => ({
  background: isDark ? 'rgba(15, 23, 42, 0.8)' : 'rgba(231, 236, 255, 0.25)',
  border: isDark ? '1px solid rgba(59, 130, 246, 0.3)' : '1.5px solid rgba(231, 236, 255, 0.45)',
  borderRadius: '20px',
  padding: '24px 20px',
  backdropFilter: 'blur(14px)',
  color: isDark ? '#f8fafc' : '#1e3c72',
  boxShadow: isDark ?
    '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)' :
    '0 12px 32px rgba(0, 0, 0, 0.08)',
  transition: 'transform 0.4s ease, box-shadow 0.4s ease',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 18px 48px rgba(0, 0, 0, 0.12)',
  },
  [theme.breakpoints.down('sm')]: {
    width: '100%',
    maxWidth: '100%',
    height: 'auto',
    margin: '0 auto',
  },
}));

const Customers = () => {
  const { t } = useLanguage();
  const { isDarkMode } = useThemeMode();
  const testimonials = createTestimonials(t);

  const [currentIndex, setCurrentIndex] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    let interval;
    if (isMobile) {
      interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % testimonials.length);
      }, 5000);
    }
    return () => clearInterval(interval);
  }, [isMobile]);

  const slideVariants = {
    enter: { x: 400, opacity: 0, scale: 0.95 },
    center: { x: 0, opacity: 1, scale: 1, transition: { duration: 0.9, ease: [0.25, 0.1, 0.25, 1] } },
    exit: { x: -400, opacity: 0, scale: 0.95, transition: { duration: 0.9, ease: [0.25, 0.1, 0.25, 1] } },
  };

  return (
    <Section id="customers" isDark={isDarkMode}>
      <motion.div
        initial={{ opacity: 0, x: 100 }}
        whileInView={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
      >
        <Typography variant="h3" sx={{ fontWeight: '900', mb: 3, color: isDarkMode ? '#f8fafc' : '#1e3c72', textShadow: '2px 2px 4px rgba(0, 0, 0, 0.2)' }}>
          {t('customers.title')}
        </Typography>
        <Typography
          variant="h6"
          sx={{
            maxWidth: 700,
            mx: 'auto',
            mb: 4,
            color: isDarkMode ? '#cbd5e1' : '#2a5298',
            fontStyle: 'italic',
          }}
        >
          {t('customers.subtitle')}
        </Typography>
      </motion.div>

      <GridContainer>
        {!isMobile ? (
          testimonials.map((item, i) => (
            <Card
              key={i}
              isDark={isDarkMode}
              initial={{ opacity: 0, x: 60 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: i * 0.15 }}
              viewport={{ once: true }}
            >
              <Typography variant="body1" sx={{ fontSize: '1.05rem', mb: 2, fontStyle: 'italic', color: isDarkMode ? '#cbd5e1' : 'inherit' }}>
                “{item.text}”
              </Typography>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: isDarkMode ? '#3b82f6' : '#2a5298', mb: 1 }}>
                {item.name} – {item.role}
              </Typography>
              <Typography variant="body2" sx={{ color: isDarkMode ? '#94a3b8' : '#1e3c72', opacity: 0.9 }}>
                {item.rating}
              </Typography>
            </Card>
          ))
        ) : (
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              custom={0}
              initial="enter"
              animate="center"
              exit="exit"
              variants={slideVariants}
              transition={{ duration: 0.9, ease: [0.25, 0.1, 0.25, 1] }}
              style={{ position: 'relative', width: '100%' }}
            >
              <Card isDark={isDarkMode}>
                <Typography variant="body1" sx={{ fontSize: '1.05rem', mb: 2, fontStyle: 'italic', color: isDarkMode ? '#cbd5e1' : 'inherit' }}>
                  “{testimonials[currentIndex].text}”
                </Typography>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: isDarkMode ? '#3b82f6' : '#2a5298', mb: 1 }}>
                  {testimonials[currentIndex].name} – {testimonials[currentIndex].role}
                </Typography>
                <Typography variant="body2" sx={{ color: isDarkMode ? '#94a3b8' : '#1e3c72', opacity: 0.9 }}>
                  {testimonials[currentIndex].rating}
                </Typography>
              </Card>
            </motion.div>
          </AnimatePresence>
        )}
      </GridContainer>
    </Section>
  );
};

export default Customers;
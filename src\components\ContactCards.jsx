import React from "react";
import { Box, Typography, Grid, Paper } from "@mui/material";
import { LocationOn, Email, Phone } from "@mui/icons-material";

const contactData = [
  {
    title: "Our Address",
    icon: <LocationOn fontSize="large" />,
    details: `P. O. Box 11550\nDar es Salaam\n2nd Floor,\nLumumba Complex,\nLumumba / Mafia Street`,
  },
  {
    title: "Email Us",
    icon: <Email fontSize="large" />,
    details: `<EMAIL>`,
  },
  {
    title: "Call Us",
    icon: <Phone fontSize="large" />,
    details: `(+255) 788 606 735`,
  },
];

const ContactCards = () => {
  return (
    <Box
      sx={{
        // minHeight: "100vh",
        // width: "100%",
        position: "relative",
        top: 0,
        left: 0,
        zIndex: 1300,
        backdropFilter: "blur(120px)",
        background: "rgba(0, 0, 0, 0.4)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        px: 2,
      }}
    >
      <Paper
        elevation={12}
        sx={{
          p: 5,
          borderRadius: 5,
          maxWidth: 800,
          width: "100%",
          background: "rgba(255, 255, 255, 0.12)",
          backdropFilter: "blur(220px)",
          color: "#fff",
          boxShadow: "0 12px 48px rgba(0,0,0,0.5)",
        }}
      >
        <Typography
          variant="h5"
          align="center"
          gutterBottom
          sx={{ fontWeight: "bold", mb: 4 }}
        >
          Get in Touch with Us
        </Typography>

        <Grid container spacing={4}>
          {contactData.map((item, index) => (
            <Grid item xs={12} sm={4} key={index}>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  textAlign: "center",
                  p: 2,
                }}
              >
                <Box
                  sx={{
                    width: 64,
                    height: 64,
                    borderRadius: "50%",
                    backgroundColor: "#2563EB",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    mb: 2,
                    boxShadow: "0 4px 12px rgba(0,0,0,0.3)",
                  }}
                >
                  {item.icon}
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                  {item.title}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ whiteSpace: "pre-line", fontSize: 14 }}
                >
                  {item.details}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Paper>
    </Box>
  );
};

export default ContactCards;

import React from "react";
import { <PERSON>, Typo<PERSON>, But<PERSON> } from "@mui/material";
import { styled } from "@mui/system";
import { useThemeMode } from "../contexts/ThemeContext";
import { useLanguage } from "../contexts/LanguageContext";
import ladyImage from "../assets/images/blog-1.jpg";
import TRA from "../assets/logos/tra-logo.png";
import BOT from "../assets/logos/bot-logo.png";
import MINISTRY from "../assets/logos/ministry-logo.png";
import SSRA from "../assets/logos/ssra.png";

// Hero Wrapper
const HeroWrapper = styled(Box)(({ theme, isDark }) => ({
  position: "relative",
  display: "flex",
  width: "100%",
  minHeight: "100vh",
  alignItems: "center",
  backgroundColor: isDark ? "transparent" : "#f5f9ff",
  background: isDark ?
    `linear-gradient(135deg,
      rgba(15, 23, 42, 0.97) 0%,
      rgba(30, 41, 59, 0.95) 25%,
      rgba(51, 65, 85, 0.93) 50%,
      rgba(71, 85, 105, 0.95) 75%,
      rgba(100, 116, 139, 0.97) 100%
    ),
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.1) 0%, transparent 50%)` :
    "#f5f9ff",
  overflow: "hidden",
  marginTop: "76px",
  transition: "all 0.3s ease",
  [theme.breakpoints.down("md")]: {
    flexDirection: "column",
    minHeight: "60vh",
  },
}));

// Background Image
const BackgroundImage = styled("img")(({ theme }) => ({
  position: "absolute",
  top: 0,
  left: 0,
  width: "100%",
  height: "100%",
  objectFit: "cover",
  zIndex: 1,
  [theme.breakpoints.down("md")]: {
    height: "60vh",
  },
}));

// Content Box
const ContentBox = styled(Box)(({ theme, isDark }) => ({
  position: "relative",
  zIndex: 2,
  width: "50%",
  backgroundColor: isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(255, 255, 255, 0.75)",
  borderRadius: "24px",
  padding: "48px",
  marginLeft: "32px",
  boxShadow: isDark ?
    "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)" :
    "0 12px 28px rgba(0,0,0,0.12)",
  backdropFilter: "blur(20px)",
  WebkitBackdropFilter: "blur(20px)",
  border: isDark ? "1px solid rgba(255, 255, 255, 0.2)" : "none",
  fontFamily: "'Roboto', sans-serif",
  transition: "all 0.3s ease",
  [theme.breakpoints.down("md")]: {
    width: "90%",
    marginLeft: 0,
    marginTop: "20px",
    padding: "32px",
  },
}));

// Logos
const LogoContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  flexWrap: "wrap",
  justifyContent: "center",
  gap: "40px",
  marginTop: "40px",
}));

const StyledLogo = styled("img")(({ theme }) => ({
  height: "90px",
  objectFit: "contain",
  [theme.breakpoints.down("md")]: { height: "60px" },
  [theme.breakpoints.down("sm")]: { height: "40px" },
}));

// Component
const Hero = () => {
  const { isDarkMode } = useThemeMode();
  const { t } = useLanguage();

  return (
    <HeroWrapper isDark={isDarkMode}>
      <BackgroundImage src={ladyImage} alt="Hero Background" />
      <ContentBox isDark={isDarkMode}>
        <Typography
          variant="h3"
          sx={{
            fontWeight: 800,
            color: isDarkMode ? "#f8fafc" : "#1a237e",
            fontFamily: "'Montserrat', sans-serif",
            fontSize: {
              xs: "26px",
              sm: "34px",
              md: "42px",
              lg: "48px",
              xl: "54px",
            },
            mb: 1,
          }}
        >
          {t('hero.welcome')}
        </Typography>

        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            color: isDarkMode ? "#3b82f6" : "#2a3b8f",
            fontFamily: "'Roboto', sans-serif",
            fontSize: {
              xs: "16px",
              sm: "20px",
              md: "22px",
              lg: "24px",
              xl: "26px",
            },
            mb: 2,
          }}
        >
          {t('hero.subtitle')}
        </Typography>

        <Typography
          variant="body2"
          sx={{
            fontSize: {
              xs: "12px",
              sm: "14px",
              md: "16px",
            },
            fontWeight: 600,
            color: isDarkMode ? "#D4AF37" : "#D4AF37",
            textAlign: "center",
            mb: 2,
            textTransform: "uppercase",
            letterSpacing: "2px",
          }}
        >
          {t('hero.tagline')}
        </Typography>

        <Typography
          variant="body1"
          sx={{
            fontSize: {
              xs: "14px",
              sm: "16px",
              md: "18px",
              lg: "20px",
            },
            lineHeight: 1.6,
            color: isDarkMode ? "#cbd5e1" : "#444",
            maxWidth: "600px",
            fontFamily: "'Roboto', sans-serif",
            mb: 4,
          }}
        >
          {t('hero.description')}
        </Typography>

        <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
          <Button
            variant="contained"
            sx={{
              backgroundColor: isDarkMode ? "#3b82f6" : "#1a237e",
              color: "#fff",
              fontSize: "14px",
              px: 3,
              py: 1.5,
              fontWeight: 600,
              borderRadius: "12px",
              textTransform: "none",
              "&:hover": {
                backgroundColor: isDarkMode ? "#2563eb" : "#0d1136",
              },
            }}
          >
            {t('hero.getStarted')}
          </Button>
          <Button
            variant="outlined"
            sx={{
              fontSize: "14px",
              px: 3,
              py: 1.5,
              fontWeight: 500,
              borderColor: isDarkMode ? "#3b82f6" : "#1a237e",
              color: isDarkMode ? "#3b82f6" : "#1a237e",
              borderRadius: "12px",
              textTransform: "none",
              "&:hover": {
                backgroundColor: isDarkMode ? "rgba(59, 130, 246, 0.1)" : "#e8eaf6",
                borderColor: isDarkMode ? "#2563eb" : "#0d1136",
              },
            }}
          >
            {t('common.contactUs')}
          </Button>
        </Box>

        <LogoContainer>
          <StyledLogo src={TRA} alt="TRA" />
          <StyledLogo src={BOT} alt="BOT" />
          <StyledLogo src={MINISTRY} alt="Ministry" />
          <StyledLogo src={SSRA} alt="SSRA" />
        </LogoContainer>
      </ContentBox>
    </HeroWrapper>
  );
};

export default Hero;

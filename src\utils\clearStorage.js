// Utility to clear potentially corrupted localStorage data
export const clearCorruptedStorage = () => {
  try {
    // Check if theme-mode is corrupted
    const themeData = localStorage.getItem('theme-mode');
    if (themeData && themeData !== 'true' && themeData !== 'false' && themeData !== 'dark' && themeData !== 'light') {
      try {
        JSON.parse(themeData);
      } catch (e) {
        console.warn('Clearing corrupted theme data:', themeData);
        localStorage.removeItem('theme-mode');
      }
    }

    // Check if language data is valid
    const langData = localStorage.getItem('language');
    if (langData && langData !== 'en' && langData !== 'sw') {
      console.warn('Clearing invalid language data:', langData);
      localStorage.removeItem('language');
    }
  } catch (error) {
    console.warn('Error checking localStorage:', error);
  }
};

// Call this function when the app starts
export const initializeStorage = () => {
  clearCorruptedStorage();
};

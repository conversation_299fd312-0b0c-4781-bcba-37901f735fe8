# Nelainey Consulting - Professional Website

<div align="center">

![Nelainey Consulting](https://img.shields.io/badge/Nelainey-Consulting-blue?style=for-the-badge&logo=react)
![React](https://img.shields.io/badge/React-19.1.0-61DAFB?style=for-the-badge&logo=react)
![Material-UI](https://img.shields.io/badge/Material--UI-7.1.1-0081CB?style=for-the-badge&logo=material-ui)
![Framer Motion](https://img.shields.io/badge/Framer_Motion-12.18.1-0055FF?style=for-the-badge&logo=framer)

**A modern, professional consulting firm website built with React, featuring advanced animations, multi-language support, and responsive design.**

[Live Demo](#) • [Documentation](FEATURES.md) • [Getting Started](#getting-started)

</div>

---

## Table of Contents

- [Features](#features)
- [Tech Stack](#tech-stack)
- [Getting Started](#getting-started)
- [Project Structure](#project-structure)
- [Design System](#design-system)
- [Multi-Language Support](#multi-language-support)
- [Responsive Design](#responsive-design)
- [Performance](#performance)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

---

## Features

### Core Features
- **Dual Theme System** - Professional light/dark mode with smooth transitions
- **Multi-Language Support** - English and Kiswahili (Swahili) localization
- **Fully Responsive** - Optimized for all devices and screen sizes
- **Advanced Animations** - Smooth Framer Motion animations and transitions
- **Modern UI/UX** - Material-UI v5 with custom glassmorphism effects
- **Performance Optimized** - Lazy loading, code splitting, and performance monitoring

### Business Sections
- **Hero Section** - Compelling introduction with animated elements
- **Statistics** - Dynamic counters showcasing company achievements
- **About Us** - Comprehensive company information and values
- **Services** - Interactive service cards with detailed descriptions
- **Our Team** - Professional team showcase with social links
- **Client Testimonials** - Customer feedback and success stories
- **Contact** - Multiple contact methods and information
- **Footer** - Fixed bottom navigation with company links

### Technical Features
- **Theme Context** - Centralized theme management
- **Language Context** - Complete translation system
- **Mobile-First Design** - Progressive enhancement approach
- **Smooth Navigation** - Anchor-based scrolling with animations
- **Persistent Storage** - Theme and language preferences saved
- **SEO Optimized** - Semantic HTML and meta tags

---

## Tech Stack

### Frontend Framework
- **React 19.1.0** - Latest React with concurrent features
- **React DOM 19.1.0** - Modern rendering engine

### UI & Styling
- **Material-UI 7.1.1** - Comprehensive component library
- **Emotion** - CSS-in-JS styling solution
- **Tailwind CSS 4.1.8** - Utility-first CSS framework
- **Framer Motion 12.18.1** - Production-ready motion library

### Icons & Assets
- **Material-UI Icons** - Consistent icon system
- **React Icons 5.5.0** - Popular icon libraries
- **Heroicons** - Beautiful hand-crafted SVG icons

### Development Tools
- **React Scripts 5.0.1** - Build toolchain
- **Testing Library** - Comprehensive testing utilities
- **Web Vitals** - Performance monitoring
- **ESLint** - Code quality and consistency

---

## Getting Started

### Prerequisites
- Node.js 16.0.0 or higher
- npm 7.0.0 or higher (or yarn 1.22.0+)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Euphrase8/nelaineyy.git
   cd nelaineyy
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start development server**
   ```bash
   npm start
   # or
   yarn start
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Available Scripts

| Command | Description |
|---------|-------------|
| `npm start` | Runs the app in development mode |
| `npm test` | Launches the test runner |
| `npm run build` | Builds the app for production |
| `npm run eject` | Ejects from Create React App (irreversible) |

---

## Project Structure

```
nelaineyy/
├── public/                    # Static assets
│   ├── favicon.ico            # Site favicon
│   ├── BOT92.png             # App icon
│   ├── index.html            # HTML template
│   └── manifest.json         # PWA manifest
├── src/                      # Source code
│   ├── components/           # React components
│   │   ├── Navbar.jsx        # Navigation component
│   │   ├── Hero.jsx          # Hero section
│   │   ├── AboutUs.jsx       # About section
│   │   ├── OurServices.jsx   # Services showcase
│   │   ├── OurTeam.jsx       # Team section
│   │   ├── Stats.jsx         # Statistics display
│   │   ├── Features.jsx      # Features section
│   │   ├── Customers.jsx     # Client testimonials
│   │   ├── Footer.jsx        # Footer component
│   │   └── ThemeWrapper.jsx  # Theme wrapper
│   ├── contexts/             # React contexts
│   │   ├── ThemeContext.js   # Theme management
│   │   └── LanguageContext.js # Translation system
│   ├── utils/                # Utility functions
│   │   ├── performance.js    # Performance hooks
│   │   └── clearStorage.js   # Storage management
│   ├── assets/               # Images and media
│   │   └── services/         # Service images
│   ├── App.js                # Main application
│   ├── index.js              # Entry point
│   └── App.test.js           # App tests
├── package.json              # Dependencies and scripts
├── README.md                 # This file
├── FEATURES.md               # Detailed features documentation
└── .gitignore                # Git ignore rules
```

---

## Design System

### Color Palette

#### Light Theme
```css
Primary: #1e3c72 (Navy Blue)
Secondary: #2a5298 (Royal Blue)
Background: #fafafa (Light Gray)
Surface: #ffffff (White)
Text Primary: #1e3c72 (Navy)
Text Secondary: #4b5563 (Gray)
```

#### Dark Theme
```css
Primary: #3b82f6 (Blue)
Secondary: #2563eb (Blue Variant)
Background: Linear gradient with rgba(15, 23, 42, 0.97)
Surface: rgba(255, 255, 255, 0.1) with glassmorphism
Text Primary: #f8fafc (Light Gray)
Text Secondary: #cbd5e1 (Medium Gray)
```

### Typography
- **Primary Font**: 'Inter', 'Roboto', 'Montserrat'
- **Headings**: 700-900 font weight
- **Body Text**: 400-500 font weight
- **Responsive Scaling**: Fluid typography across breakpoints

### Animations
- **Easing**: `cubic-bezier(0.175, 0.885, 0.32, 1.275)`
- **Duration**: 0.6s for major transitions, 0.3s for micro-interactions
- **Stagger**: 0.1s delay between animated elements
- **Scroll Animations**: Triggered on viewport intersection

---

## Multi-Language Support

### Supported Languages
- **English** (Default)
- **Kiswahili** (Swahili)

### Implementation
```jsx
// Using the language context
import { useLanguage } from './contexts/LanguageContext';

const { t, changeLanguage, currentLanguage } = useLanguage();
const welcomeText = t('hero.welcome'); // Returns translated text
```

### Translation Coverage
- Navigation menu
- Hero section content
- About Us information
- Service descriptions
- Team member details
- Statistics labels
- Footer content
- Call-to-action buttons

---

## Responsive Design

### Breakpoints
```css
Mobile: < 600px (xs)
Tablet: 600px - 960px (sm-md)
Desktop: 960px - 1280px (lg)
Large Desktop: > 1280px (xl)
```

### Mobile Optimizations
- Touch-friendly navigation with hamburger menu
- Optimized tap targets (minimum 44px)
- Responsive images with lazy loading
- Reduced motion for performance
- Stacked layouts for better mobile UX

### Performance Features
- Lazy loading for non-critical components
- Code splitting for optimal bundle size
- Image optimization and compression
- Performance monitoring with Web Vitals
- Efficient state management

---

## Performance

### Optimization Techniques
- **Lazy Loading**: Non-critical components loaded on demand
- **Code Splitting**: Automatic bundle splitting by React
- **Memoization**: React.memo and useMemo for expensive operations
- **Debouncing**: Search and scroll event optimization
- **Image Optimization**: WebP format with fallbacks

### Performance Metrics
```
First Contentful Paint: < 1.5s
Largest Contentful Paint: < 2.5s
Cumulative Layout Shift: < 0.1
First Input Delay: < 100ms
Time to Interactive: < 3.5s
```

### Monitoring
```jsx
import { usePerformanceMonitor } from './utils/performance';

// Automatic performance tracking
usePerformanceMonitor();
```

---

## Testing

### Testing Framework
- **React Testing Library** - Component testing
- **Jest** - Test runner and assertions
- **User Event** - User interaction simulation

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm test -- --watch

# Run tests with coverage
npm test -- --coverage
```

### Test Coverage
- Component rendering
- User interactions
- Theme switching
- Language switching
- Navigation functionality

---

## Deployment

### Build for Production
```bash
npm run build
```

### Deployment Platforms
- **Netlify** - Recommended for static hosting
- **Vercel** - Excellent for React applications
- **GitHub Pages** - Free hosting for public repositories
- **AWS S3** - Scalable cloud hosting

---

## Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Commit your changes**
   ```bash
   git commit -m 'Add amazing feature'
   ```
4. **Push to the branch**
   ```bash
   git push origin feature/amazing-feature
   ```
5. **Open a Pull Request**

### Development Guidelines
- Follow the existing code style
- Write meaningful commit messages
- Add tests for new features
- Update documentation as needed

---

## Author

**Joachim Julius Euphrase**
- GitHub: [@Euphrase8](https://github.com/Euphrase8)
- Email: <EMAIL>

---

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## Support & Contact

**Nelainey Consulting**
- Website: [nelainey-consulting.com](#)
- Email: <EMAIL>
- Phone: +255 788 606 735
- Location: Tanzania, East Africa

---

<div align="center">

**Built with passion by Joachim Julius Euphrase**

**Star this repository if you found it helpful!**

</div>

import React, { createContext, useContext, useState, useEffect } from 'react';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';

const ThemeContext = createContext();

export const useThemeMode = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useThemeMode must be used within a ThemeContextProvider');
  }
  return context;
};

export const ThemeContextProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    try {
      const saved = localStorage.getItem('theme-mode');
      if (saved === null) return false;

      // Handle legacy string values
      if (saved === 'dark') return true;
      if (saved === 'light') return false;

      return JSON.parse(saved);
    } catch (error) {
      console.warn('Error parsing theme from localStorage:', error);
      // Clear corrupted data
      localStorage.removeItem('theme-mode');
      return false;
    }
  });

  useEffect(() => {
    try {
      localStorage.setItem('theme-mode', JSON.stringify(isDarkMode));
    } catch (error) {
      console.warn('Error saving theme to localStorage:', error);
    }
  }, [isDarkMode]);

  const toggleTheme = () => {
    setIsDarkMode(prev => !prev);
  };

  // Light theme colors (current default)
  const lightTheme = {
    primary: {
      main: '#1a237e',
      light: '#3f51b5',
      dark: '#0d1136',
    },
    secondary: {
      main: '#D4AF37',
      light: '#f3f4f6',
      dark: '#B8860B',
    },
    background: {
      default: '#f5f9ff',
      paper: 'rgba(255, 255, 255, 0.8)',
      hero: '#f5f9ff',
      section: '#ffffff',
    },
    text: {
      primary: '#111827',
      secondary: '#4b5563',
      accent: '#1a237e',
    },
    navbar: {
      background: 'rgba(255, 255, 255, 0.8)',
      text: '#111827',
      hover: '#1e40af',
    },
    topStrip: {
      background: '#f3f4f6',
      text: '#4b5563',
    }
  };

  // Dark theme colors (matching your current dark sections)
  const darkTheme = {
    primary: {
      main: '#3b82f6',
      light: '#60a5fa',
      dark: '#1d4ed8',
    },
    secondary: {
      main: '#D4AF37',
      light: '#fbbf24',
      dark: '#B8860B',
    },
    background: {
      default: 'linear-gradient(135deg, rgba(15, 23, 42, 0.97) 0%, rgba(30, 41, 59, 0.95) 25%, rgba(51, 65, 85, 0.93) 50%, rgba(71, 85, 105, 0.95) 75%, rgba(100, 116, 139, 0.97) 100%)',
      paper: 'rgba(255, 255, 255, 0.1)',
      hero: 'linear-gradient(135deg, rgba(15, 23, 42, 0.97) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(51, 65, 85, 0.93) 100%)',
      section: 'linear-gradient(135deg, rgba(15, 23, 42, 0.97) 0%, rgba(30, 41, 59, 0.95) 25%, rgba(51, 65, 85, 0.93) 50%, rgba(71, 85, 105, 0.95) 75%, rgba(100, 116, 139, 0.97) 100%)',
    },
    text: {
      primary: '#f8fafc',
      secondary: '#cbd5e1',
      accent: '#3b82f6',
    },
    navbar: {
      background: 'rgba(15, 23, 42, 0.9)',
      text: '#f8fafc',
      hover: '#3b82f6',
    },
    topStrip: {
      background: 'rgba(15, 23, 42, 0.95)',
      text: '#cbd5e1',
    }
  };

  const currentTheme = isDarkMode ? darkTheme : lightTheme;

  const muiTheme = createTheme({
    palette: {
      mode: isDarkMode ? 'dark' : 'light',
      primary: currentTheme.primary,
      secondary: currentTheme.secondary,
      background: {
        default: isDarkMode ? '#0f172a' : '#f5f9ff',
        paper: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.8)',
      },
      text: {
        primary: currentTheme.text.primary,
        secondary: currentTheme.text.secondary,
      },
    },
    typography: {
      fontFamily: "'Inter', 'Roboto', 'Montserrat', sans-serif",
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            background: isDarkMode ? 
              'linear-gradient(135deg, rgba(15, 23, 42, 0.97) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(51, 65, 85, 0.93) 100%)' : 
              '#f5f9ff',
            minHeight: '100vh',
          },
        },
      },
    },
  });

  const value = {
    isDarkMode,
    toggleTheme,
    theme: currentTheme,
    muiTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      <ThemeProvider theme={muiTheme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </ThemeContext.Provider>
  );
};

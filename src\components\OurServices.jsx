import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, styled, useTheme, useMediaQuery, Container } from '@mui/material';
import { motion, AnimatePresence, useInView } from 'framer-motion';
import { AutoAwesome, TrendingUp, Security } from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';

import img1 from '../assets/services/planning.jpg';
import img2 from '../assets/services/accounting.jpg';
import img3 from '../assets/services/tax.jpg';
import img4 from '../assets/services/assurance.jpg';
import img5 from '../assets/services/ict.jpg';
import img6 from '../assets/services/train.jpg';

const createServices = (t) => [
  {
    title: t('services.financial.title'),
    tagline: t('services.financial.tagline'),
    description: t('services.financial.description'),
    image: img1,
  },
  {
    title: t('services.accounting.title'),
    tagline: t('services.accounting.tagline'),
    description: t('services.accounting.description'),
    image: img2,
  },
  {
    title: t('services.tax.title'),
    tagline: t('services.tax.tagline'),
    description: t('services.tax.description'),
    image: img3,
  },
  {
    title: t('services.assurance.title'),
    tagline: t('services.assurance.tagline'),
    description: t('services.assurance.description'),
    image: img4,
  },
  {
    title: t('services.ict.title'),
    tagline: t('services.ict.tagline'),
    description: t('services.ict.description'),
    image: img5,
  },
  {
    title: t('services.training.title'),
    tagline: t('services.training.tagline'),
    description: t('services.training.description'),
    image: img6,
  },
];

const Section = styled(Box)(({ theme }) => ({
  marginTop: '80px',
  padding: '80px 0',
  background: `
    linear-gradient(135deg,
      rgba(15, 23, 42, 0.97) 0%,
      rgba(30, 41, 59, 0.95) 25%,
      rgba(51, 65, 85, 0.93) 50%,
      rgba(71, 85, 105, 0.95) 75%,
      rgba(100, 116, 139, 0.97) 100%
    ),
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.1) 0%, transparent 50%)
  `,
  position: 'relative',
  overflow: 'hidden',
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      )
    `,
    pointerEvents: 'none',
  },
  [theme.breakpoints.down('md')]: {
    padding: '60px 0',
    marginTop: '60px',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '40px 0',
    marginTop: '40px',
  },
}));

const Grid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gap: '32px',
  marginTop: '60px',
  padding: '0 20px',
  gridTemplateColumns: 'repeat(3, 1fr)',
  width: '100%',
  maxWidth: '1600px',
  marginLeft: 'auto',
  marginRight: 'auto',
  position: 'relative',
  justifyItems: 'center',
  [theme.breakpoints.up('xl')]: {
    gridTemplateColumns: 'repeat(3, 1fr)',
    gap: '40px',
    maxWidth: '1800px',
  },
  [theme.breakpoints.down('lg')]: {
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: '28px',
  },
  [theme.breakpoints.down('md')]: {
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: '24px',
    padding: '0 16px',
  },
  [theme.breakpoints.down('sm')]: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '20px',
    padding: '0 12px',
    overflow: 'visible',
  },
}));

const CardWrapper = styled(Box)(({ theme }) => ({
  perspective: '2000px',
  width: '100%',
  maxWidth: '420px',
  height: '600px',
  margin: '0 auto',
  position: 'relative',
  transformStyle: 'preserve-3d',
  [theme.breakpoints.up('xl')]: {
    maxWidth: '450px',
    height: '620px',
  },
  [theme.breakpoints.down('lg')]: {
    maxWidth: '380px',
    height: '580px',
  },
  [theme.breakpoints.down('md')]: {
    maxWidth: '360px',
    height: '560px',
  },
  [theme.breakpoints.down('sm')]: {
    maxWidth: '320px',
    height: '520px',
    margin: '0 auto',
  },
}));

const Card = styled(motion.div)(({ theme }) => ({
  position: 'absolute',
  width: '100%',
  height: '100%',
  transformStyle: 'preserve-3d',
  transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  cursor: 'pointer',
  borderRadius: '24px',
  background: `
    linear-gradient(145deg,
      rgba(255, 255, 255, 0.25) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0.05) 100%
    )
  `,
  border: '1px solid rgba(255, 255, 255, 0.2)',
  backdropFilter: 'blur(20px)',
  WebkitBackdropFilter: 'blur(20px)',
  boxShadow: `
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2)
  `,
  willChange: 'transform, box-shadow',
  '&:hover': {
    transform: 'translateY(-8px) scale(1.02)',
    boxShadow: `
      0 35px 70px -12px rgba(0, 0, 0, 0.35),
      0 0 0 1px rgba(255, 255, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 50px rgba(59, 130, 246, 0.3)
    `,
    '& .card-content': {
      transform: 'translateY(-5px)',
    },
  },
  '&:active': {
    transform: 'translateY(-4px) scale(0.98)',
  },
  [theme.breakpoints.down('sm')]: {
    borderRadius: '20px',
  },
}));

const CardFace = styled(Box)(({ back, theme }) => ({
  position: 'absolute',
  width: '100%',
  height: '100%',
  borderRadius: '24px',
  background: back
    ? `linear-gradient(145deg,
        rgba(15, 23, 42, 0.95) 0%,
        rgba(30, 41, 59, 0.9) 50%,
        rgba(51, 65, 85, 0.95) 100%
      )`
    : `linear-gradient(145deg,
        rgba(255, 255, 255, 0.25) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 100%
      )`,
  border: back
    ? '1px solid rgba(59, 130, 246, 0.3)'
    : '1px solid rgba(255, 255, 255, 0.2)',
  color: back ? '#f8fafc' : '#f8fafc',
  padding: '32px 24px',
  backfaceVisibility: 'hidden',
  transform: back ? 'rotateY(180deg)' : 'rotateY(0deg)',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  alignItems: 'center',
  textAlign: 'center',
  boxSizing: 'border-box',
  backdropFilter: 'blur(25px)',
  WebkitBackdropFilter: 'blur(25px)',
  overflow: 'hidden',
  transition: 'transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: back
      ? 'radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)'
      : 'radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)',
    pointerEvents: 'none',
  },
  [theme.breakpoints.down('sm')]: {
    borderRadius: '20px',
    padding: '28px 20px',
  },
}));

const Image = styled('img')(({ theme }) => ({
  width: '100%',
  height: '220px', // Reduced height to ensure button visibility
  objectFit: 'cover',
  borderRadius: '16px',
  marginBottom: '16px',
  objectPosition: 'center',
  transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  filter: 'brightness(1.1) contrast(1.1) saturate(1.2)',
  border: '2px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
  '&:hover': {
    transform: 'scale(1.02)',
    filter: 'brightness(1.2) contrast(1.2) saturate(1.3)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.18)',
  },
  [theme.breakpoints.down('md')]: {
    height: '200px',
  },
  [theme.breakpoints.down('sm')]: {
    height: '180px',
    borderRadius: '12px',
  },
}));

const ReadMoreButton = styled(motion.button)(({ theme }) => ({
  marginTop: '20px',
  padding: '12px 28px',
  background: `linear-gradient(135deg,
    #3b82f6 0%,
    #2563eb 50%,
    #1d4ed8 100%
  )`,
  color: '#ffffff',
  border: 'none',
  borderRadius: '50px',
  cursor: 'pointer',
  fontWeight: '700',
  fontSize: '0.875rem',
  letterSpacing: '0.5px',
  textTransform: 'uppercase',
  transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  alignSelf: 'center',
  position: 'relative',
  overflow: 'hidden',
  boxShadow: '0 8px 25px rgba(59, 130, 246, 0.4)',
  zIndex: 2, // Ensure button is above other elements
  flexShrink: 0, // Prevent button from shrinking
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent)',
    transition: 'left 0.6s ease',
  },
  '&:hover': {
    background: `linear-gradient(135deg,
      #2563eb 0%,
      #1d4ed8 50%,
      #1e40af 100%
    )`,
    transform: 'translateY(-2px) scale(1.05)',
    boxShadow: '0 12px 35px rgba(59, 130, 246, 0.6)',
    '&::before': {
      left: '100%',
    },
  },
  '&:active': {
    transform: 'translateY(0) scale(0.98)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '10px 24px',
    fontSize: '0.8rem',
    marginTop: '16px',
  },
}));

const ExpandedContentOverlay = styled(motion.div)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  background: `
    linear-gradient(145deg,
      rgba(15, 23, 42, 0.98) 0%,
      rgba(30, 41, 59, 0.96) 50%,
      rgba(51, 65, 85, 0.98) 100%
    )
  `,
  borderRadius: '24px',
  padding: '32px 24px',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center', // Center vertically
  alignItems: 'center', // Center horizontally
  textAlign: 'center',
  backdropFilter: 'blur(25px)',
  WebkitBackdropFilter: 'blur(25px)',
  border: '1px solid rgba(59, 130, 246, 0.3)',
  zIndex: 10,
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.15) 0%, transparent 50%)',
    pointerEvents: 'none',
  },
  [theme.breakpoints.down('sm')]: {
    borderRadius: '20px',
    padding: '28px 20px',
  },
}));

const OurServices = () => {
  const { t } = useLanguage();
  const SERVICES = createServices(t);

  const [currentIndex, setCurrentIndex] = useState(0);
  const [expandedIndex, setExpandedIndex] = useState(null);
  const [isPaused, setIsPaused] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const gridRef = useRef(null);
  const sectionRef = useRef(null);
  const isInView = useInView(gridRef, { once: false, amount: 0.2 });
  const sectionInView = useInView(sectionRef, { once: true, amount: 0.1 });

  // Auto-scroll for mobile view
  useEffect(() => {
    let interval;
    if (isMobile && isInView && !isPaused && expandedIndex === null) {
      interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % SERVICES.length);
      }, 4000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isMobile, isInView, isPaused, expandedIndex, SERVICES.length]);

  // Animation variants for mobile slide
  const slideVariants = {
    enter: {
      x: 300,
      opacity: 0,
      scale: 0.9,
    },
    center: {
      x: 0,
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275],
      },
    },
    exit: {
      x: -300,
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: 0.6,
        ease: [0.175, 0.885, 0.32, 1.275],
      },
    },
  };

  // Enhanced animation variants matching OurTeam
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 60,
      scale: 0.9,
      rotateX: 15,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      rotateX: 0,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275]
      },
    },
  };

  return (
    <Section id="our-services" ref={sectionRef}>
      <Container maxWidth="xl">
        <motion.div
          initial="hidden"
          animate={sectionInView ? "visible" : "hidden"}
          variants={{
            hidden: { opacity: 0, y: 30, scale: 0.95 },
            visible: {
              opacity: 1,
              y: 0,
              scale: 1,
              transition: { duration: 0.8, ease: [0.175, 0.885, 0.32, 1.275], staggerChildren: 0.1 },
            },
          }}
          style={{ textAlign: 'center', marginBottom: '40px' }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 50 }}
            animate={sectionInView ? { opacity: 1, scale: 1, y: 0 } : {}}
            transition={{ duration: 1, ease: [0.175, 0.885, 0.32, 1.275] }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
              <AutoAwesome sx={{ fontSize: '2rem', color: '#3b82f6', mr: 2 }} />
              <Typography
                variant="h2"
                sx={{
                  fontWeight: '900',
                  background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
                  fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                  letterSpacing: '-0.02em',
                }}
              >
                {t('services.title')}
              </Typography>
              <TrendingUp sx={{ fontSize: '2rem', color: '#10b981', ml: 2 }} />
            </Box>
            <Typography
              variant="h5"
              sx={{
                maxWidth: 800,
                mx: 'auto',
                color: '#cbd5e1',
                fontWeight: '400',
                lineHeight: 1.6,
                fontSize: { xs: '1.1rem', md: '1.3rem' },
                opacity: 0.9,
              }}
            >
              {t('services.subtitle')}
            </Typography>
          </motion.div>
        </motion.div>

        <Box ref={gridRef} onMouseEnter={() => setIsPaused(true)} onMouseLeave={() => setIsPaused(false)}>
          <motion.div
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={containerVariants}
          >
            <Grid>
              {isMobile ? (
                <AnimatePresence initial={false} mode="wait">
                  <motion.div
                    key={currentIndex}
                    variants={slideVariants}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    style={{
                      position: 'relative',
                      width: '100%',
                      willChange: 'transform, opacity',
                    }}
                  >
                    <CardWrapper>
                      <Card
                        whileHover={{
                          scale: 1.02,
                          transition: { duration: 0.3, ease: [0.175, 0.885, 0.32, 1.275] },
                        }}
                        style={{ position: 'relative' }}
                      >
                        <CardFace className="card-content">
                          <Box sx={{ position: 'relative', zIndex: 1 }}>
                            <Image
                              src={SERVICES[currentIndex].image}
                              alt={SERVICES[currentIndex].title}
                              loading="lazy"
                            />
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, justifyContent: 'center' }}>
                              <Security sx={{ fontSize: '1rem', color: '#3b82f6', mr: 1 }} />
                              <Typography
                                variant="caption"
                                sx={{
                                  opacity: 0.8,
                                  fontSize: '0.85rem',
                                  fontWeight: '600',
                                  color: '#64748b',
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.5px',
                                }}
                              >
                                {SERVICES[currentIndex].tagline}
                              </Typography>
                            </Box>
                            <Typography
                              variant="h5"
                              sx={{
                                fontWeight: '800',
                                mb: 2,
                                color: '#f8fafc',
                                fontSize: { xs: '1.3rem', md: '1.5rem' },
                                lineHeight: 1.2,
                              }}
                            >
                              {SERVICES[currentIndex].title}
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                fontSize: { xs: '0.85rem', md: '0.95rem' },
                                lineHeight: 1.5,
                                color: '#cbd5e1',
                                mb: 2,
                                overflow: 'hidden',
                                display: '-webkit-box',
                                WebkitLineClamp: 4, // Limit to 4 lines to ensure button visibility
                                WebkitBoxOrient: 'vertical',
                              }}
                            >
                              {SERVICES[currentIndex].description}
                            </Typography>
                          </Box>
                          <ReadMoreButton
                            onClick={() => setExpandedIndex(expandedIndex === currentIndex ? null : currentIndex)}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            {t('services.learnMore')}
                          </ReadMoreButton>
                        </CardFace>

                        <AnimatePresence>
                          {expandedIndex === currentIndex && (
                            <ExpandedContentOverlay
                              initial={{ opacity: 0, scale: 0.9 }}
                              animate={{ opacity: 1, scale: 1 }}
                              exit={{ opacity: 0, scale: 0.9 }}
                              transition={{ duration: 0.4, ease: [0.175, 0.885, 0.32, 1.275] }}
                            >
                              <Box
                                sx={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  height: '100%',
                                  textAlign: 'center',
                                  px: 2,
                                }}
                              >
                                <Typography
                                  variant="h5"
                                  sx={{
                                    fontWeight: '800',
                                    mb: 2,
                                    color: '#f8fafc',
                                    fontSize: { xs: '1.3rem', md: '1.5rem' },
                                  }}
                                >
                                  {SERVICES[currentIndex].title}
                                </Typography>
                                <Typography
                                  variant="body1"
                                  sx={{
                                    fontSize: { xs: '0.9rem', md: '1rem' },
                                    lineHeight: 1.7,
                                    color: '#cbd5e1',
                                    mb: 2,
                                  }}
                                >
                                  {SERVICES[currentIndex].description}
                                  <br />
                                  <strong style={{ color: '#3b82f6' }}>Ready to transform your business?</strong>
                                  <br />
                                  Contact our expert team for personalized solutions tailored to your specific needs.
                                </Typography>
                                <ReadMoreButton
                                  onClick={() => setExpandedIndex(null)}
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  style={{
                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%)',
                                    boxShadow: '0 8px 25px rgba(239, 68, 68, 0.4)',
                                  }}
                                >
                                  ← {t('services.close')}
                                </ReadMoreButton>
                              </Box>
                            </ExpandedContentOverlay>
                          )}
                        </AnimatePresence>
                      </Card>
                    </CardWrapper>
                  </motion.div>
                </AnimatePresence>
              ) : (
                SERVICES.map((service, index) => (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    style={{
                      position: 'relative',
                      width: '100%',
                      willChange: 'transform, opacity',
                    }}
                  >
                    <CardWrapper>
                      <Card
                        whileHover={{
                          scale: 1.02,
                          transition: { duration: 0.3, ease: [0.175, 0.885, 0.32, 1.275] },
                        }}
                        style={{ position: 'relative' }}
                      >
                        <CardFace className="card-content">
                          <Box sx={{ position: 'relative', zIndex: 1 }}>
                            <Image
                              src={service.image}
                              alt={service.title}
                              loading="lazy"
                            />
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, justifyContent: 'center' }}>
                              <Security sx={{ fontSize: '1rem', color: '#3b82f6', mr: 1 }} />
                              <Typography
                                variant="caption"
                                sx={{
                                  opacity: 0.8,
                                  fontSize: '0.85rem',
                                  fontWeight: '600',
                                  color: '#64748b',
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.5px',
                                }}
                              >
                                {service.tagline}
                              </Typography>
                            </Box>
                            <Typography
                              variant="h5"
                              sx={{
                                fontWeight: '800',
                                mb: 2,
                                color: '#f8fafc',
                                fontSize: { xs: '1.3rem', md: '1.5rem' },
                                lineHeight: 1.2,
                              }}
                            >
                              {service.title}
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                fontSize: { xs: '0.85rem', md: '0.95rem' },
                                lineHeight: 1.5,
                                color: '#cbd5e1',
                                mb: 2,
                                overflow: 'hidden',
                                display: '-webkit-box',
                                WebkitLineClamp: 4, // Limit to 4 lines to ensure button visibility
                                WebkitBoxOrient: 'vertical',
                              }}
                            >
                              {service.description}
                            </Typography>
                          </Box>
                          <ReadMoreButton
                            onClick={() => setExpandedIndex(expandedIndex === index ? null : index)}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            {t('services.learnMore')}
                          </ReadMoreButton>
                        </CardFace>

                        <AnimatePresence>
                          {expandedIndex === index && (
                            <ExpandedContentOverlay
                              initial={{ opacity: 0, scale: 0.9 }}
                              animate={{ opacity: 1, scale: 1 }}
                              exit={{ opacity: 0, scale: 0.9 }}
                              transition={{ duration: 0.4, ease: [0.175, 0.885, 0.32, 1.275] }}
                            >
                              <Box
                                sx={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  height: '100%',
                                  textAlign: 'center',
                                  px: 2,
                                }}
                              >
                                <Typography
                                  variant="h5"
                                  sx={{
                                    fontWeight: '800',
                                    mb: 2,
                                    color: '#f8fafc',
                                    fontSize: { xs: '1.3rem', md: '1.5rem' },
                                  }}
                                >
                                  {service.title}
                                </Typography>
                                <Typography
                                  variant="body1"
                                  sx={{
                                    fontSize: { xs: '0.9rem', md: '1rem' },
                                    lineHeight: 1.7,
                                    color: '#cbd5e1',
                                    mb: 2,
                                  }}
                                >
                                  {service.description}
                                  <br />
                                  <strong style={{ color: '#3b82f6' }}>Ready to transform your business?</strong>
                                  <br />
                                  Contact our expert team for personalized solutions tailored to your specific needs.
                                </Typography>
                                <ReadMoreButton
                                  onClick={() => setExpandedIndex(null)}
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  style={{
                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%)',
                                    boxShadow: '0 8px 25px rgba(239, 68, 68, 0.4)',
                                  }}
                                >
                                  ← {t('services.close')}
                                </ReadMoreButton>
                              </Box>
                            </ExpandedContentOverlay>
                          )}
                        </AnimatePresence>
                      </Card>
                    </CardWrapper>
                  </motion.div>
                ))
              )}
            </Grid>
          </motion.div>
        </Box>
      </Container>
    </Section>
  );
};

export default OurServices;
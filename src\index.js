import React from 'react';
   import ReactDOM from 'react-dom/client'; // Updated import
   import { ThemeProvider, createTheme } from '@mui/material/styles';
   import App from './App';

   // Create a default theme (customize as needed)
   const theme = createTheme();

   const root = ReactDOM.createRoot(document.getElementById('root'));
   root.render(
     <ThemeProvider theme={theme}>
       <App />
     </ThemeProvider>
   );
import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  IconButton,
  Paper,
  Popper,
  Fade,
  Dialog,
  useMediaQuery,
  useTheme,

  Menu,
  MenuItem,
} from "@mui/material";
import { styled } from "@mui/system";
import { motion, AnimatePresence } from "framer-motion";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import EmailIcon from "@mui/icons-material/Email";
import HomeIcon from "@mui/icons-material/Home";
import InfoIcon from "@mui/icons-material/Info";
import VideoLibraryIcon from "@mui/icons-material/VideoLibrary";
import PublicIcon from "@mui/icons-material/Public";
import ContactMailIcon from "@mui/icons-material/ContactMail";
import LanguageIcon from "@mui/icons-material/Language";
import DarkModeIcon from "@mui/icons-material/DarkMode";
import LightModeIcon from "@mui/icons-material/LightMode";

import { useThemeMode } from "../contexts/ThemeContext";
import { useLanguage } from "../contexts/LanguageContext";
import AboutUs from "./AboutUs";
import ContactCards from "./ContactCards";

const TopStrip = styled(Box)(({ theme, isDark }) => ({
  backgroundColor: isDark ? "rgba(15, 23, 42, 0.95)" : "#f3f4f6",
  padding: "4px 12px",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  color: isDark ? "#cbd5e1" : "#4b5563",
  fontSize: "10px",
  position: "fixed",
  top: 0,
  width: "100%",
  zIndex: 1101,
  transition: "all 0.3s ease",
}));

const StyledAppBar = styled(AppBar)(({ theme, isDark }) => ({
  top: "24px",
  position: "fixed",
  background: isDark ? "rgba(15, 23, 42, 0.9)" : "rgba(255, 255, 255, 0.8)",
  backdropFilter: "blur(10px)",
  WebkitBackdropFilter: "blur(10px)",
  boxShadow: "0 2px 6px rgba(0, 0, 0, 0.1)",
  zIndex: 1100,
  transition: "all 0.3s ease",
}));

const StyledToolbar = styled(Toolbar)({
  display: "flex",
  justifyContent: "space-between",
  padding: "0 12px",
  minHeight: "48px",
  flexWrap: "wrap",
});

const NavLink = styled(Button)(({ theme, isDark }) => ({
  color: isDark ? "#f8fafc" : "#111827",
  textTransform: "none",
  fontSize: "12px",
  display: "flex",
  alignItems: "center",
  gap: "4px",
  padding: "4px 8px",
  transition: "all 0.3s ease",
  borderRadius: "12px",
  border: "1px solid transparent",
  "&:hover": {
    color: isDark ? "#3b82f6" : "#1e40af",
    background: isDark ? "rgba(59, 130, 246, 0.1)" : "rgba(255, 255, 255, 0.9)",
    backdropFilter: "blur(10px)",
    transform: "scale(1.15)",
    boxShadow: "0 4px 20px rgba(0, 0, 0, 0.1)",
    border: "1px solid #D4AF37",
  },
}));

const NavMenuContainer = styled(Paper)({
  display: "flex",
  flexWrap: "wrap",
  gap: "8px",
  alignItems: "center",
  justifyContent: "center",
  padding: "8px 12px",
  borderRadius: "20px",
  backdropFilter: "blur(10px)",
  WebkitBackdropFilter: "blur(10px)",
  backgroundColor: "rgba(255, 255, 255, 0.6)",
  boxShadow: "0 4px 12px rgba(0,0,0,0.2)",
});

const DialogContent = styled(Box)(({ theme }) => ({
  background: "rgba(255, 255, 255, 0.25)",
  border: "1px solid rgba(255,255,255,0.3)",
  borderRadius: "20px",
  backdropFilter: "blur(20px)",
  WebkitBackdropFilter: "blur(20px)",
  boxShadow: "0 8px 32px rgba(31, 38, 135, 0.2)",
  padding: theme.spacing(4),
  margin: theme.spacing(2),
}));

// Removed unused CountryDropdown component

const Logo = styled("span")({
  display: "inline-flex",
  alignItems: "center",
  marginRight: "8px",
  "& svg": {
    width: "40px",
    height: "40px",
  },
});

const Navbar = () => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [hoverAbout, setHoverAbout] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [aboutDialogOpen, setAboutDialogOpen] = useState(false);
  const [contactDialogOpen, setContactDialogOpen] = useState(false);
  const [languageAnchorEl, setLanguageAnchorEl] = useState(null);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const { isDarkMode, toggleTheme } = useThemeMode();
  const { t, currentLanguage, changeLanguage } = useLanguage();

  const toggleMenu = () => setMobileOpen(!mobileOpen);

  const handleLanguageClick = (event) => {
    setLanguageAnchorEl(event.currentTarget);
  };

  const handleLanguageClose = () => {
    setLanguageAnchorEl(null);
  };

  const handleLanguageChange = (lang) => {
    changeLanguage(lang);
    handleLanguageClose();
  };

  const navItems = [
    { label: t('nav.home'), icon: <HomeIcon fontSize="small" />, anchor: "#home" },
    { label: t('nav.about'), icon: <InfoIcon fontSize="small" />, anchor: "#about" },
    { label: t('nav.services'), icon: <VideoLibraryIcon fontSize="small" />, anchor: "#services" },
    { label: t('nav.team'), icon: <PublicIcon fontSize="small" />, anchor: "#team" },
    { label: t('nav.contact'), icon: <ContactMailIcon fontSize="small" />, anchor: "#contact" },
  ];

  return (
    <>
      <TopStrip isDark={isDarkMode}>
        <Box sx={{ display: "flex", alignItems: "center", gap: "4px" }}>
          <LocationOnIcon fontSize="small" />
          <Typography sx={{ fontSize: "10px" }}>
            Our Location | Lumumba Complex - Mafia St.
          </Typography>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
          {/* Theme Toggle */}
          <IconButton
            onClick={toggleTheme}
            size="small"
            sx={{
              color: isDarkMode ? "#cbd5e1" : "#4b5563",
              '&:hover': { color: isDarkMode ? "#3b82f6" : "#1e40af" }
            }}
          >
            {isDarkMode ? <LightModeIcon fontSize="small" /> : <DarkModeIcon fontSize="small" />}
          </IconButton>

          {/* Language Toggle */}
          <Button
            onClick={handleLanguageClick}
            size="small"
            sx={{
              color: isDarkMode ? "#cbd5e1" : "#4b5563",
              fontSize: "10px",
              minWidth: "auto",
              '&:hover': { color: isDarkMode ? "#3b82f6" : "#1e40af" }
            }}
          >
            <LanguageIcon fontSize="small" sx={{ mr: 0.5 }} />
            {currentLanguage === 'en' ? 'EN' : 'SW'}
          </Button>

          <Box sx={{ display: "flex", alignItems: "center", gap: "4px" }}>
            <EmailIcon fontSize="small" />
            <Typography sx={{ fontSize: "10px" }}><EMAIL></Typography>
          </Box>
        </Box>
      </TopStrip>

      <StyledAppBar isDark={isDarkMode}>
        <StyledToolbar>
          <Typography
            variant="h5"
            sx={{
              fontSize: { xs: "14px", sm: "16px", md: "18px" },
              fontWeight: 700,
              color: isDarkMode ? "#3b82f6" : "#1E3A8A",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Logo>
              <svg width="80" height="80" viewBox="0 0 80 80">
                <text
                  x="50%" y="50%" dominantBaseline="middle"
                  textAnchor="middle" fill={isDarkMode ? "#3b82f6" : "#1E3A8A"} fontSize="36" fontWeight="800" fontFamily="Arial"
                >
                  N|C
                </text>
              </svg>
            </Logo>
            Nelainey Consulting
          </Typography>

          <Box sx={{ display: { xs: "none", sm: "flex" }, flex: 1, justifyContent: "center" }}>
            <NavMenuContainer>
              {navItems.map((item, index) => (
                <motion.div
                  key={item.label}
                  initial={{ opacity: 0, y: -10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                >
                  {item.label === t('nav.about') ? (
                    <Box
                      onMouseEnter={(e) => {
                        if (!isMobile) {
                          setHoverAbout(true);
                          setAnchorEl(e.currentTarget);
                        }
                      }}
                      onMouseLeave={() => !isMobile && setHoverAbout(false)}
                      onClick={() => isMobile && setAboutDialogOpen(true)}
                      sx={{ position: "relative", zIndex: 1500 }}
                    >
                      <NavLink isDark={isDarkMode} startIcon={item.icon}>{item.label}</NavLink>
                      {!isMobile && (
                        <Popper open={hoverAbout} anchorEl={anchorEl} transition placement="bottom-start">
                          {({ TransitionProps }) => (
                            <Fade {...TransitionProps} timeout={250}>
                              <Box
                                sx={{
                                  p: 3,
                                  background: "rgba(255, 255, 255, 0.95)",
                                  borderRadius: 3,
                                  boxShadow: 6,
                                  width: 600,
                                  maxWidth: "90vw",
                                }}
                              >
                                <AboutUs />
                              </Box>
                            </Fade>
                          )}
                        </Popper>
                      )}
                    </Box>
                  ) : item.label === t('nav.contact') ? (
                    <Box
                      onMouseEnter={(e) => {
                        if (!isMobile) {
                          setAnchorEl(e.currentTarget);
                          setContactDialogOpen(true);
                        }
                      }}
                      onMouseLeave={() => {
                        if (!isMobile) {
                          setContactDialogOpen(false);
                        }
                      }}
                      onClick={() => isMobile && setContactDialogOpen(true)}
                      sx={{ position: "relative", zIndex: 1500 }}
                    >
                      <NavLink isDark={isDarkMode} startIcon={item.icon}>{item.label}</NavLink>
                      {!isMobile && (
                        <Popper open={contactDialogOpen} anchorEl={anchorEl} transition placement="bottom-start">
                          {({ TransitionProps }) => (
                            <Fade {...TransitionProps} timeout={250}>
                              <Box
                                sx={{
                                  p: 3,
                                  background: "rgba(255, 255, 255, 0.95)",
                                  borderRadius: 3,
                                  boxShadow: 6,
                                  width: 400,
                                  maxWidth: "95vw",
                                }}
                              >
                                <ContactCards />
                              </Box>
                            </Fade>
                          )}
                        </Popper>
                      )}
                    </Box>
                  ) : (
                    <NavLink isDark={isDarkMode} href={item.anchor} startIcon={item.icon}>
                      {item.label}
                    </NavLink>
                  )}
                </motion.div>
              ))}
            </NavMenuContainer>
          </Box>

          <Box sx={{ display: { xs: "flex", sm: "none" } }}>
            <IconButton onClick={toggleMenu}>
              {mobileOpen ? <CloseIcon /> : <MenuIcon />}
            </IconButton>
          </Box>
        </StyledToolbar>

        <AnimatePresence>
          {mobileOpen && (
            <motion.div
              key="mobile-menu"
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.9 }}
              transition={{ duration: 0.3 }}
              style={{
                padding: "16px",
                margin: "12px",
                borderRadius: "20px",
                backdropFilter: "blur(20px)",
                WebkitBackdropFilter: "blur(20px)",
                background: "linear-gradient(135deg, rgba(255,255,255,0.35), rgba(255,255,255,0.1))",
                boxShadow: "0 8px 32px rgba(31, 38, 135, 0.2)",
                border: "1px solid rgba(255, 255, 255, 0.3)",
              }}
            >
              {navItems.map((item, index) => (
                <motion.div
                  key={item.label}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.4 }}
                  style={{ marginBottom: "12px" }}
                >
                  <NavLink href={item.anchor} startIcon={item.icon}>
                    {item.label}
                  </NavLink>
                </motion.div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </StyledAppBar>

      {/* About Dialog (Mobile Only) */}
      <Dialog open={aboutDialogOpen} onClose={() => setAboutDialogOpen(false)} fullScreen>
        <DialogContent>
          <AboutUs />
        </DialogContent>
      </Dialog>

      {/* Contact Dialog (Mobile Only) */}
      {isMobile && (
        <Dialog open={contactDialogOpen} onClose={() => setContactDialogOpen(false)} fullScreen>
          <DialogContent>
            <ContactCards />
          </DialogContent>
        </Dialog>
      )}

      {/* Language Menu */}
      <Menu
        anchorEl={languageAnchorEl}
        open={Boolean(languageAnchorEl)}
        onClose={handleLanguageClose}
        PaperProps={{
          sx: {
            background: isDarkMode ? 'rgba(15, 23, 42, 0.95)' : 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
          }
        }}
      >
        <MenuItem
          onClick={() => handleLanguageChange('en')}
          sx={{
            color: isDarkMode ? '#f8fafc' : '#111827',
            '&:hover': { background: isDarkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.1)' }
          }}
        >
          🇺🇸 English
        </MenuItem>
        <MenuItem
          onClick={() => handleLanguageChange('sw')}
          sx={{
            color: isDarkMode ? '#f8fafc' : '#111827',
            '&:hover': { background: isDarkMode ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.1)' }
          }}
        >
          🇹🇿 Kiswahili
        </MenuItem>
      </Menu>
    </>
  );
};

export default Navbar;

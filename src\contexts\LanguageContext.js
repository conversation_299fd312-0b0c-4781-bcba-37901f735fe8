import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Translation data
const translations = {
  en: {
    // Navigation
    nav: {
      home: 'Home',
      about: 'About',
      services: 'Services',
      team: 'Team',
      contact: 'Contact',
      language: 'Language',
    },
    // Hero Section
    hero: {
      welcome: 'Welcome to Nelainey',
      subtitle: 'Consultancy',
      tagline: 'Experience | Expert | Excellence',
      description: 'Your trusted partner for comprehensive business consulting solutions in Tanzania and East Africa.',
      getStarted: 'Get Started',
      learnMore: 'Learn More',
    },
    // About Section
    about: {
      title: 'About Nelainey Consulting',
      subtitle: 'Welcome to Nelainey Consulting – a premier Tanzania-based professional consulting firm dedicated to transforming businesses with tailored expertise.',
      description: 'Since our founding in 2010, Nelainey Consulting has served over 500 clients across Tanzania, East Africa, and beyond, helping startups build financial foundations and multinational corporations navigate complex regulatory landscapes.',
      stats: {
        clients: 'Clients Served',
        experience: 'Years Experience',
        satisfaction: 'Client Satisfaction',
        support: 'Support Available',
      }
    },
    // Services Section
    services: {
      title: 'Our Premium Services',
      subtitle: 'Experience world-class consulting solutions designed to elevate your business to unprecedented heights',
      financial: {
        title: 'Financial Planning',
        tagline: 'Expertise • Ongoing Support • 500+ Clients',
        description: 'Comprehensive financial strategies to optimize your business finances, including budgeting and investment advice tailored to your goals.',
      },
      accounting: {
        title: 'Accounting Services',
        tagline: 'Precision • Compliance • 300+ Audits',
        description: 'Accurate bookkeeping, financial reporting, and audit services to ensure compliance and support informed business decisions.',
      },
      tax: {
        title: 'Tax Consulting',
        tagline: 'Strategic • Regulatory • 400+ Cases',
        description: 'Expert tax planning and compliance to minimize liabilities and maximize benefits under the latest regulations across Tanzania and beyond.',
      },
      assurance: {
        title: 'Assurance Services',
        tagline: 'Reliable • Tech-Driven • 250+ Projects',
        description: 'Guaranteed reliability in financial statements with advanced assurance techniques to build trust with stakeholders.',
      },
      ict: {
        title: 'ICT Solutions',
        tagline: 'Innovative • Secure • 150+ Solutions',
        description: 'Cutting-edge ICT services, including cybersecurity and technology integration, to enhance your business operations.',
      },
      training: {
        title: 'Corporate Training',
        tagline: 'Training • Development • 100+ Programs',
        description: 'Tailored training programs to upskill your team in financial, tax, and consulting practices for long-term success.',
      },
      learnMore: 'Learn More',
      close: 'Close',
    },
    // Team Section
    team: {
      title: 'Meet Our Expert Team',
      subtitle: 'Discover the extraordinary professionals driving innovation and excellence in every project',
    },
    // Stats Section
    stats: {
      clients: 'Clients Served',
      projects: 'Projects Completed',
      experience: 'Years of Experience',
      support: 'Support Hours Daily',
    },
    // Footer
    footer: {
      company: 'Nelainey Consulting',
      description: 'Your trusted partner for comprehensive business consulting solutions.',
      quickLinks: 'Quick Links',
      services: 'Services',
      contact: 'Contact Info',
      followUs: 'Follow Us',
      rights: 'All rights reserved.',
    },
    // Common
    common: {
      readMore: 'Read More',
      contactUs: 'Contact Us',
      getQuote: 'Get Quote',
      viewAll: 'View All',
    }
  },
  sw: {
    // Navigation
    nav: {
      home: 'Nyumbani',
      about: 'Kuhusu',
      services: 'Huduma',
      team: 'Timu',
      contact: 'Mawasiliano',
      language: 'Lugha',
    },
    // Hero Section
    hero: {
      welcome: 'Karibu Nelainey',
      subtitle: 'Ushauri',
      tagline: 'Uzoefu | Mtaalamu | Ubora',
      description: 'Mshirika wako wa kuaminika kwa suluhisho kamili za ushauri wa biashara Tanzania na Afrika Mashariki.',
      getStarted: 'Anza Sasa',
      learnMore: 'Jifunze Zaidi',
    },
    // About Section
    about: {
      title: 'Kuhusu Nelainey Consulting',
      subtitle: 'Karibu Nelainey Consulting – kampuni kuu ya ushauri wa kitaalamu iliyo Tanzania inayojitoa kubadilisha biashara kwa utaalamu maalum.',
      description: 'Tangu kuanzishwa kwetu mnamo 2010, Nelainey Consulting imehudumia wateja zaidi ya 500 kote Tanzania, Afrika Mashariki, na zaidi, ikisaidia biashara ndogo kujenga misingi ya kifedha na makampuni ya kimataifa kupitia mazingira magumu ya kisheria.',
      stats: {
        clients: 'Wateja Waliohudumiwa',
        experience: 'Miaka ya Uzoefu',
        satisfaction: 'Kuridhika kwa Wateja',
        support: 'Msaada Unapatikana',
      }
    },
    // Services Section
    services: {
      title: 'Huduma Zetu Bora',
      subtitle: 'Pata suluhisho za ushauri za kiwango cha kimataifa zilizoundwa kuinua biashara yako kwa viwango visivyowahi kutokea',
      financial: {
        title: 'Mipango ya Kifedha',
        tagline: 'Utaalamu • Msaada Unaoendelea • Wateja 500+',
        description: 'Mikakati kamili ya kifedha kuboresha fedha za biashara yako, ikiwa ni pamoja na bajeti na ushauri wa uwekezaji unaofaa malengo yako.',
      },
      accounting: {
        title: 'Huduma za Uhasibu',
        tagline: 'Usahihi • Kufuata Sheria • Ukaguzi 300+',
        description: 'Uwekaji wa kumbukumbu sahihi, ripoti za kifedha, na huduma za ukaguzi kuhakikisha kufuata sheria na kusaidia maamuzi sahihi ya biashara.',
      },
      tax: {
        title: 'Ushauri wa Kodi',
        tagline: 'Kimkakati • Kisheria • Kesi 400+',
        description: 'Mipango ya kitaalamu ya kodi na kufuata sheria kupunguza majukumu na kuongeza faida chini ya kanuni za hivi karibuni kote Tanzania na zaidi.',
      },
      assurance: {
        title: 'Huduma za Uhakika',
        tagline: 'Kuaminika • Teknolojia • Miradi 250+',
        description: 'Uhakika wa kuhakikishwa katika taarifa za kifedha kwa mbinu za kihakika za juu kujenga imani na wadau.',
      },
      ict: {
        title: 'Suluhisho za TEHAMA',
        tagline: 'Ubunifu • Usalama • Suluhisho 150+',
        description: 'Huduma za TEHAMA za kisasa, ikiwa ni pamoja na usalama wa mtandao na uunganishaji wa teknolojia, kuboresha shughuli za biashara yako.',
      },
      training: {
        title: 'Mafunzo ya Makampuni',
        tagline: 'Mafunzo • Maendeleo • Programu 100+',
        description: 'Programu za mafunzo zilizoundwa maalum kuboresha ujuzi wa timu yako katika mazoezi ya kifedha, kodi, na ushauri kwa mafanikio ya muda mrefu.',
      },
      learnMore: 'Jifunze Zaidi',
      close: 'Funga',
    },
    // Team Section
    team: {
      title: 'Kutana na Timu Yetu ya Wataalamu',
      subtitle: 'Gundua wataalamu wa kipekee wanaoendesha uvumbuzi na ubora katika kila mradi',
    },
    // Stats Section
    stats: {
      clients: 'Wateja Waliohudumiwa',
      projects: 'Miradi Iliyokamilika',
      experience: 'Miaka ya Uzoefu',
      support: 'Masaa ya Msaada Kila Siku',
    },
    // Footer
    footer: {
      company: 'Nelainey Consulting',
      description: 'Mshirika wako wa kuaminika kwa suluhisho kamili za ushauri wa biashara.',
      quickLinks: 'Viungo vya Haraka',
      services: 'Huduma',
      contact: 'Maelezo ya Mawasiliano',
      followUs: 'Tufuate',
      rights: 'Haki zote zimehifadhiwa.',
    },
    // Common
    common: {
      readMore: 'Soma Zaidi',
      contactUs: 'Wasiliana Nasi',
      getQuote: 'Pata Bei',
      viewAll: 'Ona Zote',
    }
  }
};

export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    try {
      const saved = localStorage.getItem('language');
      return saved || 'en';
    } catch (error) {
      console.warn('Error reading language from localStorage:', error);
      return 'en';
    }
  });

  useEffect(() => {
    try {
      localStorage.setItem('language', currentLanguage);
    } catch (error) {
      console.warn('Error saving language to localStorage:', error);
    }
  }, [currentLanguage]);

  const changeLanguage = (lang) => {
    setCurrentLanguage(lang);
  };

  const t = (key) => {
    const keys = key.split('.');
    let value = translations[currentLanguage];
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    return value || key;
  };

  const value = {
    currentLanguage,
    changeLanguage,
    t,
    isSwahili: currentLanguage === 'sw',
    isEnglish: currentLanguage === 'en',
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
